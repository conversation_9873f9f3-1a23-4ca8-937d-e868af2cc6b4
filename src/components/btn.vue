<template>
  <div
    :class="[
      'gradient-button-wrapper',
      {
        disabled: disabled,
        loading: loading,
      },
    ]"
  >
    <button class="gradient-button" :disabled="disabled || loading" @click="handleClick">
      <span v-if="loading" class="loading-spinner"></span>
      <span :class="{ 'loading-text': loading }">
        <slot>{{ text }}</slot>
      </span>
    </button>
  </div>
</template>

<script setup lang="ts" name="GradientButton">
interface Props {
  text?: string;
  disabled?: boolean;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  text: "Copy Link",
  disabled: false,
  loading: false,
});

const emit = defineEmits<{
  click: [event: MouseEvent];
}>();

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit("click", event);
  }
};
</script>

<style lang="scss" scoped>
.gradient-button {
  position: relative;
  border: none;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  z-index: 1;

  // 渐变背景
  background: linear-gradient(90deg, #ffbd55 0%, #ff572a 100%);

  font-family: "Inter";
  font-weight: 700;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;

  min-width: 200px;
  min-height: 56px;

  // 渐变边框效果
  &::before {
    content: "";
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 52px;
    background: linear-gradient(85deg, #fff0bf 0%, #ffe1c3 50%, #ffd3c8 100%);
    z-index: -1;
  }

  // 文字样式
  color: #fff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  // 按下效果
  &:active:not(.disabled):not(.loading) {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
  }

  // 禁用状态
  &.disabled {
    opacity: 0.6;
    transform: none;

    &::before {
      background: linear-gradient(135deg, #d3d3d3 0%, #a9a9a9 100%);
    }

    background: linear-gradient(135deg, #d3d3d3 0%, #a9a9a9 100%);
  }

  // 加载状态
  &.loading {
    cursor: wait;

    .loading-text {
      opacity: 0.7;
    }
  }

  // 加载动画
  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  // 焦点样式
  &:focus-visible {
    outline: 2px solid #ffb347;
    outline-offset: 2px;
  }
}
</style>
