# 渐变按钮组件 (GradientButton)

一个具有渐变背景和边框的 Vue 3 按钮组件，基于您提供的设计图片创建。

## 特性

- ✨ 渐变背景色（橙黄色到橙红色）
- 🎨 渐变边框效果
- 📱 响应式设计
- 🎯 多种尺寸支持
- 🔄 加载状态
- ❌ 禁用状态
- 🎪 悬停和点击动画效果
- ♿ 无障碍支持

## 使用方法

### 基础用法

```vue
<template>
  <GradientButton @click="handleClick">Copy Link</GradientButton>
</template>

<script setup>
import GradientButton from "@/components/btn.vue";

const handleClick = () => {
  console.log("按钮被点击了！");
};
</script>
```

### 基础样式

```vue
<template>
  <GradientButton>Default Button</GradientButton>
  <GradientButton text="Custom Text" />
</template>
```

### 自定义文本

```vue
<template>
  <GradientButton text="Download Now" @click="handleDownload" />
</template>
```

### 不同状态

```vue
<template>
  <!-- 正常状态 -->
  <GradientButton @click="handleClick">Normal</GradientButton>

  <!-- 禁用状态 -->
  <GradientButton disabled>Disabled</GradientButton>

  <!-- 加载状态 -->
  <GradientButton loading>Loading</GradientButton>
</template>
```

### 使用插槽

```vue
<template>
  <GradientButton @click="handleClick">
    <span>🚀</span>
    <span>Launch App</span>
  </GradientButton>
</template>
```

## Props

| 属性名   | 类型    | 默认值      | 说明                         |
| -------- | ------- | ----------- | ---------------------------- |
| text     | string  | 'Copy Link' | 按钮文本（当没有使用插槽时） |
| disabled | boolean | false       | 是否禁用                     |
| loading  | boolean | false       | 是否显示加载状态             |

## Events

| 事件名 | 参数       | 说明         |
| ------ | ---------- | ------------ |
| click  | MouseEvent | 按钮点击事件 |

## 样式定制

组件使用 SCSS 编写，主要的颜色变量：

- 主渐变：`linear-gradient(90deg, #FFBD55 0%, #FF572A 100%)`
- 边框渐变：`linear-gradient(85deg, #FFF0BF 0%, #FFE1C3 50%, #FFD3C8 100%)`
- 悬停效果：增强的渐变和阴影效果

## 样式规格

- **高度**: 48px
- **字体**: Inter, 700 weight, 16px
- **内边距**: 12px
- **圆角**: 50px

## 浏览器兼容性

- Chrome 88+
- Firefox 87+
- Safari 14+
- Edge 88+

## 演示

访问 `/button-demo` 路由查看完整的组件演示。
