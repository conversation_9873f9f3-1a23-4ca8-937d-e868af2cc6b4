<template>
  <div class="example-container">
    <h1>渐变按钮组件示例</h1>

    <!-- 基础用法 -->
    <section class="section">
      <h2>基础用法</h2>
      <GradientButton @click="copyLink">Copy Link</GradientButton>
    </section>

    <!-- 基础样式 -->
    <section class="section">
      <h2>基础样式</h2>
      <div class="button-row">
        <GradientButton text="Standard Button" @click="handleClick" />
        <GradientButton text="Another Style" @click="handleClick" />
      </div>
    </section>

    <!-- 不同状态 -->
    <section class="section">
      <h2>不同状态</h2>
      <div class="button-row">
        <GradientButton text="Normal" @click="handleClick" />
        <GradientButton text="Disabled" disabled />
        <GradientButton text="Loading" :loading="isLoading" @click="toggleLoading" />
      </div>
    </section>

    <!-- 实际应用场景 -->
    <section class="section">
      <h2>实际应用场景</h2>
      <div class="button-row">
        <GradientButton text="Download App" @click="downloadApp" />
        <GradientButton text="Get Started" @click="getStarted" />
        <GradientButton text="Learn More" @click="learnMore" />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import GradientButton from "./btn.vue";

const isLoading = ref(false);

const copyLink = () => {
  const url = window.location.href;
  navigator.clipboard
    .writeText(url)
    .then(() => {
      alert("链接已复制到剪贴板！");
    })
    .catch(() => {
      alert("复制失败，请手动复制");
    });
};

const handleClick = () => {
  console.log("按钮被点击了！");
};

const toggleLoading = () => {
  isLoading.value = !isLoading.value;
  setTimeout(() => {
    isLoading.value = false;
  }, 3000);
};

const downloadApp = () => {
  alert("开始下载应用...");
};

const getStarted = () => {
  alert("欢迎开始使用！");
};

const learnMore = () => {
  alert("了解更多信息...");
};
</script>

<style lang="scss" scoped>
.example-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 40px;
  font-size: 32px;
  font-weight: 700;
}

.section {
  margin-bottom: 40px;
  padding: 30px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

h2 {
  color: #495057;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
}

.button-row {
  display: flex;
  gap: 16px;
  flex-direction: column;
  align-items: stretch;
}
</style>
