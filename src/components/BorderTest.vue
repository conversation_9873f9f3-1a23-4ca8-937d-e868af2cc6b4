<template>
  <div class="border-test">
    <h2>按钮边框测试</h2>
    
    <!-- 透明背景测试 -->
    <div class="test-section transparent">
      <h3>透明背景</h3>
      <GradientButton>Copy Link</GradientButton>
    </div>
    
    <!-- 白色背景测试 -->
    <div class="test-section white-bg">
      <h3>白色背景</h3>
      <GradientButton>Copy Link</GradientButton>
    </div>
    
    <!-- 灰色背景测试 -->
    <div class="test-section gray-bg">
      <h3>灰色背景 (#fafafa)</h3>
      <GradientButton>Copy Link</GradientButton>
    </div>
    
    <!-- 深色背景测试 -->
    <div class="test-section dark-bg">
      <h3>深色背景</h3>
      <GradientButton>Copy Link</GradientButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import GradientButton from './btn.vue'
</script>

<style lang="scss" scoped>
.border-test {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
  
  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }
  
  .test-section {
    margin-bottom: 30px;
    padding: 30px;
    border: 2px solid #ddd;
    border-radius: 8px;
    
    h3 {
      margin-bottom: 20px;
      color: #666;
    }
    
    &.transparent {
      background: transparent;
    }
    
    &.white-bg {
      background: white;
    }
    
    &.gray-bg {
      background: #fafafa;
    }
    
    &.dark-bg {
      background: #333;
      
      h3 {
        color: white;
      }
    }
  }
}
</style>
