<template>
  <div class="button-demo">
    <h2>渐变按钮组件示例</h2>

    <div class="demo-section">
      <h3>基础用法</h3>
      <GradientButton @click="handleCopyLink">Copy Link</GradientButton>
    </div>

    <div class="demo-section">
      <h3>基础样式</h3>
      <div class="button-group">
        <GradientButton @click="handleClick">Standard Button</GradientButton>
        <GradientButton text="Custom Text" @click="handleClick" />
      </div>
    </div>

    <div class="demo-section">
      <h3>不同状态</h3>
      <div class="button-group">
        <GradientButton @click="handleClick">Normal</GradientButton>
        <GradientButton disabled @click="handleClick">Disabled</GradientButton>
        <GradientButton loading @click="handleClick">Loading</GradientButton>
      </div>
    </div>

    <div class="demo-section">
      <h3>自定义文本</h3>
      <div class="button-group">
        <GradientButton text="Download Now" @click="handleDownload" />
        <GradientButton text="Get Started" @click="handleClick" />
        <GradientButton text="Learn More" @click="handleClick" />
      </div>
    </div>

    <div class="demo-section">
      <h3>使用插槽</h3>
      <GradientButton @click="handleClick">
        <span>🚀</span>
        <span>Launch App</span>
      </GradientButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import GradientButton from "./btn.vue";

const handleCopyLink = () => {
  // 模拟复制链接功能
  navigator.clipboard
    .writeText(window.location.href)
    .then(() => {
      alert("链接已复制到剪贴板！");
    })
    .catch(() => {
      alert("复制失败，请手动复制");
    });
};

const handleDownload = () => {
  alert("开始下载...");
};

const handleClick = () => {
  console.log("按钮被点击了！");
};
</script>

<style lang="scss" scoped>
.button-demo {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #333;
    margin-bottom: 30px;
    text-align: center;
  }

  h3 {
    color: #666;
    margin-bottom: 15px;
    font-size: 18px;
  }

  .demo-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 8px;
    background: transparent;
    position: relative;
  }

  .button-group {
    display: flex;
    gap: 16px;
    flex-direction: column;
    align-items: stretch;
    position: relative;
    z-index: 1;
  }
}
</style>
