import axios from "@/request/index";

export const getPayStatus = async (params: { order_id: string; user_id: string }) => {
  const res = await axios.request({
    url: "/open/api/notify/gcash/web/payment/status",
    params,
    method: "GET",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
  });
  return res.data;
};

// 获取app下载页面配置
export const getAppDownloadConfig = async () => {
  const res = await axios.request({
    url: "/open/api/base/landingpage",
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return res.data;
};

// 获取app下载链接
export const getAppDownloadLinks = async () => {
  const res = await axios.request({
    url: "/api/app/download/links",
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
  });
  return res.data;
};

// 获取服务器时间
// export const getServerTime = async () => {
//     const res = await axios.request({
//         url: '/api/v2/server/info',
//         method: 'POST',
//         headers: {
//             'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
//         }
//     })
//     return res.data
// }
