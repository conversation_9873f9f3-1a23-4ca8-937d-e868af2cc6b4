import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: "home",
      path: "/",
      component: () => import("@/pages/PayResult/index.vue"),
    },
    {
      name: "payResult",
      path: "/payResult",
      component: () => import("@/pages/PayResult/index.vue"),
    },
    {
      name: "download",
      path: "/download",
      component: () => import("@/pages/DownloadPage/index.vue"),
    },
    {
      name: "buttonDemo",
      path: "/button-demo",
      component: () => import("@/components/ButtonDemo.vue"),
    },
    {
      name: "borderTest",
      path: "/border-test",
      component: () => import("@/components/BorderTest.vue"),
    },
  ],
});

export default router;
