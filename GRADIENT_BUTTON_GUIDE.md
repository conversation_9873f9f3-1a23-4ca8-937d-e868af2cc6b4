# 渐变按钮组件使用指南

## 概述

根据您提供的设计图片，我创建了一个具有渐变背景和边框的 Vue 3 按钮组件。该组件完美复现了图片中的视觉效果，包括：

- 🎨 橙黄色到橙红色的渐变背景
- ✨ 浅色渐变边框效果
- 🔄 圆角设计
- 💫 悬停和点击动画
- 📱 响应式设计

## 文件结构

```
src/components/
├── btn.vue                    # 主要的渐变按钮组件
├── ButtonDemo.vue            # 完整的演示页面
├── GradientButtonExample.vue # 简化的使用示例
├── types.ts                  # TypeScript 类型定义
└── README.md                 # 组件文档
```

## 快速开始

### 1. 基础使用

```vue
<template>
  <GradientButton @click="handleClick">Copy Link</GradientButton>
</template>

<script setup>
import GradientButton from "@/components/btn.vue";

const handleClick = () => {
  console.log("按钮被点击！");
};
</script>
```

### 2. 复制链接功能（如图片所示）

```vue
<template>
  <GradientButton @click="copyCurrentLink">Copy Link</GradientButton>
</template>

<script setup>
import GradientButton from "@/components/btn.vue";

const copyCurrentLink = () => {
  const url = window.location.href;
  navigator.clipboard
    .writeText(url)
    .then(() => {
      alert("链接已复制到剪贴板！");
    })
    .catch(() => {
      alert("复制失败，请手动复制");
    });
};
</script>
```

## 组件特性

### 视觉效果

- **渐变背景**: 从 #FFBD55 (橙黄) 到 #FF572A (橙红)
- **渐变边框**: 多层次渐变效果，营造立体感
- **圆角设计**: 50px 圆角，符合现代设计趋势
- **阴影效果**: 悬停时显示柔和阴影

### 交互效果

- **悬停动画**: 向上移动 2px，增强阴影
- **点击反馈**: 瞬间回弹效果
- **加载状态**: 旋转动画指示器
- **禁用状态**: 灰色渐变，禁止交互

### 样式规格

- **固定尺寸**: 48px 高度，统一的视觉效果
- **字体**: Inter 字体，700 字重，16px 大小
- **内边距**: 12px，确保内容适当间距

## API 参考

### Props

| 属性     | 类型    | 默认值      | 说明             |
| -------- | ------- | ----------- | ---------------- |
| text     | string  | 'Copy Link' | 按钮显示文本     |
| disabled | boolean | false       | 是否禁用按钮     |
| loading  | boolean | false       | 是否显示加载状态 |

### Events

| 事件  | 参数       | 说明         |
| ----- | ---------- | ------------ |
| click | MouseEvent | 按钮点击事件 |

### Slots

| 插槽    | 说明                             |
| ------- | -------------------------------- |
| default | 自定义按钮内容，会覆盖 text 属性 |

## 使用示例

### 不同状态演示

```vue
<template>
  <div class="button-examples">
    <!-- 正常状态 -->
    <GradientButton @click="normalClick">Copy Link</GradientButton>

    <!-- 禁用状态 -->
    <GradientButton disabled>Disabled</GradientButton>

    <!-- 加载状态 -->
    <GradientButton :loading="isLoading" @click="startLoading">
      {{ isLoading ? "Processing..." : "Start Process" }}
    </GradientButton>

    <!-- 自定义内容 -->
    <GradientButton @click="customAction">
      <span>🚀</span>
      <span>Launch</span>
    </GradientButton>
  </div>
</template>

<script setup>
import { ref } from "vue";
import GradientButton from "@/components/btn.vue";

const isLoading = ref(false);

const normalClick = () => {
  console.log("Normal button clicked");
};

const startLoading = () => {
  isLoading.value = true;
  setTimeout(() => {
    isLoading.value = false;
  }, 3000);
};

const customAction = () => {
  console.log("Custom action triggered");
};
</script>
```

## 样式定制

如果需要修改颜色或其他样式，可以编辑 `src/components/btn.vue` 中的 SCSS 变量：

```scss
// 主要渐变色
background: linear-gradient(90deg, #ffbd55 0%, #ff572a 100%);

// 边框渐变色
background: linear-gradient(85deg, #fff0bf 0%, #ffe1c3 50%, #ffd3c8 100%);
```

## 浏览器兼容性

- Chrome 88+
- Firefox 87+
- Safari 14+
- Edge 88+

## 演示页面

项目已配置演示路由：

- `/button-demo` - 完整的组件演示
- 主页面已集成使用示例

启动项目后访问 `http://localhost:81/button-demo` 查看完整演示。

## 注意事项

1. 组件使用 Vue 3 Composition API 编写
2. 支持 TypeScript，提供完整的类型定义
3. 使用 SCSS 编写样式，确保项目已配置 Sass 支持
4. 组件已在现有的 PayResult 页面中集成使用

## 技术实现

- **框架**: Vue 3 + TypeScript
- **样式**: SCSS with scoped styles
- **动画**: CSS transitions and transforms
- **无障碍**: 支持键盘导航和焦点管理
- **响应式**: 适配移动端和桌面端
